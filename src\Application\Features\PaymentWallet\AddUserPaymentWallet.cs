using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Net.Mime;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.PaymentWallet;

public record AddUserPaymentWalletRequest(
    [Required(ErrorMessage = "Payment wallet ID is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Payment wallet ID must be between 1 and 100 characters")]
    string PaymentWalletId);

[EndpointGroupName("Payment Wallet")]
public class AddUserPaymentWalletController : ApiControllerBase
{
    /// <summary>
    /// Associates a payment wallet with a specific user. Requires admin privileges.
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("/{userId:int}")]
    [Authorize("admin")]
    [ProducesResponseType<Success>(StatusCodes.Status200OK)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [Produces(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> AddUserPaymentWallet(
        [FromRoute] int userId,
        [FromBody] AddUserPaymentWalletRequest request)
    {
        var command = new AddUserPaymentWalletCommand
        {
            UserId = userId,
            PaymentWalletId = request.PaymentWalletId
        };

        var result = await Mediator.Send(command, HttpContext.RequestAborted);

        return result.Match(id => Ok(id), Problem);
    }
}

public sealed class AddUserPaymentWalletCommand
    : IRequest<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public int UserId { get; init; }
    public string PaymentWalletId { get; init; } = null!;
}

public sealed class AddUserPaymentWalletCommandValidator : AbstractValidator<AddUserPaymentWalletCommand>
{
    public AddUserPaymentWalletCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotNull();

        RuleFor(x => x.PaymentWalletId)
            .NotEmpty().WithMessage("Payment wallet ID is required")
            .Length(1, 100).WithMessage("Payment wallet ID must be between 1 and 100 characters");
    }
}

public sealed class AddUserPaymentWalletCommandHandler(
    ApplicationDbContext dbContext)
    : IRequestHandler<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(AddUserPaymentWalletCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .Include(x => x.WalletInformation)
            .Where(x => x.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig?.WalletInformation != null)
        {
            userConfig.WalletInformation.UpdatePaymentWalletId(WalletId.Parse(request.PaymentWalletId));
        }
        else
        {
            var userWalletInformation = UserWalletInformation.Create(
                request.UserId,
                WalletId.Parse(request.PaymentWalletId));

            userConfig = UserConfig.Create(userWalletInformation);
            dbContext.UserConfigs.Add(userConfig);
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0 ? Result.Success : Error.Failure();
    }
}